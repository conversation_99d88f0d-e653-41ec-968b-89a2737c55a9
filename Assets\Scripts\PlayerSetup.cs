using UnityEngine;

public class PlayerSetup : MonoBehaviour
{
    [Header("Player Setup")]
    public Material playerMaterial;
    public Material attackIndicatorMaterial;
    
    void Start()
    {
        SetupPlayer();
    }
    
    void SetupPlayer()
    {
        // Create player visual (simple cube)
        GameObject playerVisual = GameObject.CreatePrimitive(PrimitiveType.Cube);
        playerVisual.transform.SetParent(transform);
        playerVisual.transform.localPosition = Vector3.zero;
        playerVisual.transform.localScale = Vector3.one;
        playerVisual.name = "PlayerVisual";
        
        // Apply material if provided
        if (playerMaterial != null)
        {
            playerVisual.GetComponent<Renderer>().material = playerMaterial;
        }
        else
        {
            // Create default blue material
            Material defaultMaterial = new Material(Shader.Find("Standard"));
            defaultMaterial.color = Color.blue;
            playerVisual.GetComponent<Renderer>().material = defaultMaterial;
        }
        
        // Remove the collider from visual (we'll use the main collider)
        Destroy(playerVisual.GetComponent<Collider>());
        
        // Setup main collider
        if (GetComponent<Collider>() == null)
        {
            BoxCollider mainCollider = gameObject.AddComponent<BoxCollider>();
            mainCollider.size = Vector3.one;
        }
        
        // Setup Rigidbody
        if (GetComponent<Rigidbody>() == null)
        {
            Rigidbody rb = gameObject.AddComponent<Rigidbody>();
            rb.freezeRotation = true; // Prevent physics rotation
        }
        
        // Create ground check point
        GameObject groundCheck = new GameObject("GroundCheck");
        groundCheck.transform.SetParent(transform);
        groundCheck.transform.localPosition = new Vector3(0, -0.5f, 0);
        
        // Setup PlayerController reference
        PlayerController playerController = GetComponent<PlayerController>();
        if (playerController != null)
        {
            playerController.groundCheck = groundCheck.transform;
            
            // Create attack indicator
            CreateAttackIndicator(playerController);
        }
    }
    
    void CreateAttackIndicator()
    {
        PlayerController playerController = GetComponent<PlayerController>();
        CreateAttackIndicator(playerController);
    }
    
    void CreateAttackIndicator(PlayerController playerController)
    {
        // Create attack indicator (simple sphere)
        GameObject attackIndicator = GameObject.CreatePrimitive(PrimitiveType.Sphere);
        attackIndicator.name = "AttackIndicator";
        attackIndicator.transform.SetParent(transform);
        attackIndicator.transform.localPosition = new Vector3(0, 0, 2f); // In front of player
        attackIndicator.transform.localScale = Vector3.one * 0.5f;
        
        // Make it semi-transparent
        Renderer renderer = attackIndicator.GetComponent<Renderer>();
        if (attackIndicatorMaterial != null)
        {
            renderer.material = attackIndicatorMaterial;
        }
        else
        {
            // Create default red semi-transparent material
            Material defaultAttackMaterial = new Material(Shader.Find("Standard"));
            defaultAttackMaterial.color = new Color(1f, 0f, 0f, 0.5f);
            defaultAttackMaterial.SetFloat("_Mode", 3); // Transparent mode
            defaultAttackMaterial.SetInt("_SrcBlend", (int)UnityEngine.Rendering.BlendMode.SrcAlpha);
            defaultAttackMaterial.SetInt("_DstBlend", (int)UnityEngine.Rendering.BlendMode.OneMinusSrcAlpha);
            defaultAttackMaterial.SetInt("_ZWrite", 0);
            defaultAttackMaterial.DisableKeyword("_ALPHATEST_ON");
            defaultAttackMaterial.EnableKeyword("_ALPHABLEND_ON");
            defaultAttackMaterial.DisableKeyword("_ALPHAPREMULTIPLY_ON");
            defaultAttackMaterial.renderQueue = 3000;
            renderer.material = defaultAttackMaterial;
        }
        
        // Remove collider from indicator
        Destroy(attackIndicator.GetComponent<Collider>());
        
        // Initially hide the indicator
        attackIndicator.SetActive(false);
        
        // Assign to player controller
        if (playerController != null)
        {
            playerController.attackIndicator = attackIndicator;
        }
    }
}
