using UnityEngine;
using UnityEngine.InputSystem;

public class SimpleGameManager : MonoBehaviour
{
    [Header("Player Settings")]
    public Vector3 playerSpawnPosition = new Vector3(0, 1, 0);
    public InputActionAsset playerInputActions;
    
    private GameObject playerInstance;
    
    void Start()
    {
        SetupEnvironment();
        SetupPlayer();
    }
    
    void SetupEnvironment()
    {
        // Create a simple ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10, 1, 10); // 100x100 units
        
        // Create ground material
        Material groundMaterial = new Material(Shader.Find("Standard"))
        {
            color = new Color(0.3f, 0.7f, 0.3f) // Green color
        };
        ground.GetComponent<Renderer>().material = groundMaterial;
        
        // Add some lighting
        if (FindObjectOfType<Light>() == null)
        {
            GameObject lightGO = new GameObject("Directional Light");
            Light light = lightGO.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            lightGO.transform.rotation = Quaternion.Euler(50f, -30f, 0f);
        }
    }
    
    void SetupPlayer()
    {
        // Create player GameObject
        playerInstance = new GameObject("Player");
        playerInstance.transform.position = playerSpawnPosition;
        playerInstance.tag = "Player";
        
        // Add PlayerInput component
        PlayerInput playerInput = playerInstance.AddComponent<PlayerInput>();
        if (playerInputActions != null)
        {
            playerInput.actions = playerInputActions;
        }
        
        // Add PlayerController component
        PlayerController controller = playerInstance.AddComponent<PlayerController>();
        controller.inputActions = playerInputActions;
        
        // Add PlayerSetup component to handle visual setup
        playerInstance.AddComponent<PlayerSetup>();
        
        Debug.Log("Player created successfully! Use WASD to move, Space to jump, Left Shift to dodge, Left Click to attack.");
    }
    
    void Update()
    {
        // Handle game-level input
        if (Input.GetKeyDown(KeyCode.R))
        {
            RestartGame();
        }
        
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            QuitGame();
        }
    }
    
    void RestartGame()
    {
        // Destroy current player
        if (playerInstance != null)
        {
            Destroy(playerInstance);
        }
        
        // Create new player
        SetupPlayer();
        
        Debug.Log("Game restarted! Press R to restart, ESC to quit.");
    }
    
    void QuitGame()
    {
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }
}
