using UnityEngine;
using UnityEngine.InputSystem;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class GameManager : MonoBehaviour
{
    [Header("Player Settings")]
    public GameObject playerPrefab;
    public Vector3 playerSpawnPosition = new Vector3(0, 1, 0);
    
    [Header("Environment")]
    public GameObject groundPrefab;
    
    private GameObject playerInstance;
    
    void Start()
    {
        SetupEnvironment();
        SetupPlayer();
    }
    
    void SetupEnvironment()
    {
        // Create ground if it doesn't exist
        if (GameObject.FindObjectOfType<Terrain>() == null && groundPrefab == null)
        {
            CreateSimpleGround();
        }
        else if (groundPrefab != null)
        {
            Instantiate(groundPrefab, Vector3.zero, Quaternion.identity);
        }
    }
    
    void CreateSimpleGround()
    {
        // Create a simple ground plane
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10, 1, 10); // 100x100 units
        
        // Set ground layer
        ground.layer = LayerMask.NameToLayer("Default");
        
        // Create ground material
        Material groundMaterial = new Material(Shader.Find("Standard"));
        groundMaterial.color = new Color(0.3f, 0.7f, 0.3f); // Green color
        ground.GetComponent<Renderer>().material = groundMaterial;
    }
    
    void SetupPlayer()
    {
        if (playerPrefab != null)
        {
            // Instantiate player prefab
            playerInstance = Instantiate(playerPrefab, playerSpawnPosition, Quaternion.identity);
        }
        else
        {
            // Create player from scratch
            CreatePlayer();
        }
    }
    
    void CreatePlayer()
    {
        // Create player GameObject
        playerInstance = new GameObject("Player");
        playerInstance.transform.position = playerSpawnPosition;
        playerInstance.tag = "Player";
        
        // Add PlayerInput component
        PlayerInput playerInput = playerInstance.AddComponent<PlayerInput>();
        
        // Load the input actions asset
        InputActionAsset inputActions = Resources.Load<InputActionAsset>("PlayerInputActions");

        #if UNITY_EDITOR
        if (inputActions == null)
        {
            // Try to find it in the project (Editor only)
            string[] guids = UnityEditor.AssetDatabase.FindAssets("PlayerInputActions t:InputActionAsset");
            if (guids.Length > 0)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
                inputActions = UnityEditor.AssetDatabase.LoadAssetAtPath<InputActionAsset>(path);
            }
        }
        #endif
        
        if (inputActions != null)
        {
            playerInput.actions = inputActions;
        }
        
        // Add PlayerController component
        playerInstance.AddComponent<PlayerController>();
        
        // Add PlayerSetup component to handle visual setup
        playerInstance.AddComponent<PlayerSetup>();
        
        Debug.Log("Player created successfully!");
    }
    
    void Update()
    {
        // Handle game-level input or logic here
        if (Input.GetKeyDown(KeyCode.R))
        {
            RestartGame();
        }
        
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            QuitGame();
        }
    }
    
    void RestartGame()
    {
        // Destroy current player
        if (playerInstance != null)
        {
            Destroy(playerInstance);
        }
        
        // Create new player
        SetupPlayer();
        
        Debug.Log("Game restarted!");
    }
    
    void QuitGame()
    {
        #if UNITY_EDITOR
            UnityEditor.EditorApplication.isPlaying = false;
        #else
            Application.Quit();
        #endif
    }
}
