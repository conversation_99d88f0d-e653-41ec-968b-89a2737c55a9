# 플레이어 컨트롤러 설정 가이드

## 개요
이 프로젝트는 Unity에서 기본적인 플레이어 캐릭터와 조작을 구현합니다.

## 조작법
- **이동**: W, A, S, D 키
- **점프**: 스페이스바
- **공격**: 마우스 좌클릭
- **회피**: 왼쪽 Shift 키
- **게임 재시작**: R 키
- **게임 종료**: ESC 키

## 설정 방법

### 방법 1: 자동 설정 (권장)
1. MainScene을 열기
2. 빈 GameObject를 생성하고 이름을 "GameManager"로 변경
3. SimpleGameManager 스크립트를 GameManager에 추가
4. PlayerInputActions.inputactions 파일을 SimpleGameManager의 Player Input Actions 필드에 드래그
5. 플레이 버튼을 눌러 테스트

### 방법 2: 수동 설정
1. MainScene을 열기
2. 빈 GameObject를 생성하고 이름을 "Player"로 변경
3. Player GameObject에 다음 컴포넌트들을 추가:
   - PlayerController
   - PlayerSetup
   - PlayerInput
4. PlayerInput 컴포넌트의 Actions 필드에 PlayerInputActions.inputactions 파일을 드래그
5. PlayerController의 Input Actions 필드에도 같은 파일을 드래그
6. 플레이 버튼을 눌러 테스트

## 스크립트 설명

### PlayerController.cs
- 플레이어의 이동, 점프, 공격, 회피 로직을 담당
- Input System을 사용하여 입력 처리
- Rigidbody를 사용한 물리 기반 이동

### PlayerSetup.cs
- 플레이어의 시각적 요소 자동 생성
- 큐브 모양의 플레이어 캐릭터
- 공격 범위 표시기 생성

### SimpleGameManager.cs
- 게임 환경 자동 생성 (바닥, 조명)
- 플레이어 자동 생성 및 설정
- 게임 재시작/종료 기능

### PlayerInputActions.inputactions
- Input System 액션 맵 정의
- 키보드/마우스 바인딩 설정

## 커스터마이징

### 플레이어 설정 변경
PlayerController 스크립트에서 다음 값들을 조정할 수 있습니다:
- `moveSpeed`: 이동 속도
- `jumpForce`: 점프 힘
- `dodgeForce`: 회피 힘
- `attackRange`: 공격 범위
- `attackDamage`: 공격 데미지

### 시각적 요소 변경
PlayerSetup 스크립트에서:
- `playerMaterial`: 플레이어 머티리얼
- `attackIndicatorMaterial`: 공격 표시기 머티리얼

## 문제 해결

### 플레이어가 움직이지 않는 경우
1. PlayerInput 컴포넌트에 InputActionAsset이 할당되었는지 확인
2. PlayerController의 inputActions 필드가 설정되었는지 확인
3. Console에서 에러 메시지 확인

### 점프가 작동하지 않는 경우
1. 바닥에 Collider가 있는지 확인
2. PlayerController의 Ground Check 설정 확인
3. Ground Mask 레이어 설정 확인

### 공격이 보이지 않는 경우
1. Attack Indicator가 생성되었는지 확인
2. 공격 시 빨간 구체가 잠깐 나타나는지 확인
3. Console에서 "Player attacks!" 메시지 확인
