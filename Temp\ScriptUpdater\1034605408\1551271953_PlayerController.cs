using UnityEngine;
using UnityEngine.InputSystem;

public class PlayerController : MonoBehaviour
{
    [Header("Movement Settings")]
    public float moveSpeed = 5f;
    public float jumpForce = 10f;
    public float dodgeForce = 8f;
    public float dodgeCooldown = 1f;
    
    [Header("Attack Settings")]
    public float attackRange = 2f;
    public float attackDamage = 10f;
    public float attackCooldown = 0.5f;
    
    [Header("Ground Check")]
    public Transform groundCheck;
    public float groundDistance = 0.4f;
    public LayerMask groundMask;
    
    // Components
    private Rigidbody rb;
    private PlayerInput playerInput;
    
    // Input Actions
    private InputAction moveAction;
    private InputAction jumpAction;
    private InputAction attackAction;
    private InputAction dodgeAction;
    
    // Movement variables
    private Vector2 moveInput;
    private bool isGrounded;
    private bool canDodge = true;
    private bool canAttack = true;
    
    // Attack visualization
    public GameObject attackIndicator;
    
    void Awake()
    {
        rb = GetComponent<Rigidbody>();
        playerInput = GetComponent<PlayerInput>();
        
        // Get input actions
        moveAction = playerInput.actions["Move"];
        jumpAction = playerInput.actions["Jump"];
        attackAction = playerInput.actions["Attack"];
        dodgeAction = playerInput.actions["Dodge"];
    }
    
    void OnEnable()
    {
        // Subscribe to input events
        jumpAction.performed += OnJump;
        attackAction.performed += OnAttack;
        dodgeAction.performed += OnDodge;
    }
    
    void OnDisable()
    {
        // Unsubscribe from input events
        jumpAction.performed -= OnJump;
        attackAction.performed -= OnAttack;
        dodgeAction.performed -= OnDodge;
    }
    
    void Update()
    {
        // Read movement input
        moveInput = moveAction.ReadValue<Vector2>();
        
        // Check if grounded
        isGrounded = Physics.CheckSphere(groundCheck.position, groundDistance, groundMask);
    }
    
    void FixedUpdate()
    {
        // Handle movement
        HandleMovement();
    }
    
    void HandleMovement()
    {
        // Calculate movement direction
        Vector3 movement = new Vector3(moveInput.x, 0f, moveInput.y);
        
        // Apply movement
        Vector3 velocity = rb.linearVelocity;
        velocity.x = movement.x * moveSpeed;
        velocity.z = movement.z * moveSpeed;
        rb.linearVelocity = velocity;
        
        // Rotate player to face movement direction
        if (movement.magnitude > 0.1f)
        {
            transform.rotation = Quaternion.LookRotation(movement);
        }
    }
    
    void OnJump(InputAction.CallbackContext context)
    {
        if (isGrounded)
        {
            rb.AddForce(Vector3.up * jumpForce, ForceMode.Impulse);
        }
    }
    
    void OnAttack(InputAction.CallbackContext context)
    {
        if (canAttack)
        {
            StartCoroutine(PerformAttack());
        }
    }
    
    void OnDodge(InputAction.CallbackContext context)
    {
        if (canDodge && moveInput.magnitude > 0.1f)
        {
            StartCoroutine(PerformDodge());
        }
    }
    
    System.Collections.IEnumerator PerformAttack()
    {
        canAttack = false;
        
        // Show attack indicator
        if (attackIndicator != null)
        {
            attackIndicator.SetActive(true);
        }
        
        // Perform attack logic here
        Debug.Log("Player attacks!");
        
        // Check for enemies in range
        Collider[] hitEnemies = Physics.OverlapSphere(transform.position + transform.forward * attackRange, attackRange);
        foreach (Collider enemy in hitEnemies)
        {
            if (enemy.CompareTag("Enemy"))
            {
                Debug.Log($"Hit enemy: {enemy.name}");
                // Apply damage to enemy here
            }
        }
        
        yield return new WaitForSeconds(0.2f);
        
        // Hide attack indicator
        if (attackIndicator != null)
        {
            attackIndicator.SetActive(false);
        }
        
        yield return new WaitForSeconds(attackCooldown - 0.2f);
        canAttack = true;
    }
    
    System.Collections.IEnumerator PerformDodge()
    {
        canDodge = false;
        
        // Calculate dodge direction
        Vector3 dodgeDirection = new Vector3(moveInput.x, 0f, moveInput.y).normalized;
        
        // Apply dodge force
        rb.AddForce(dodgeDirection * dodgeForce, ForceMode.Impulse);
        
        Debug.Log("Player dodges!");
        
        yield return new WaitForSeconds(dodgeCooldown);
        canDodge = true;
    }
    
    void OnDrawGizmosSelected()
    {
        // Draw ground check sphere
        if (groundCheck != null)
        {
            Gizmos.color = isGrounded ? Color.green : Color.red;
            Gizmos.DrawWireSphere(groundCheck.position, groundDistance);
        }
        
        // Draw attack range
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position + transform.forward * attackRange, attackRange);
    }
}
